import { Metadata } from "next"
import { Suspense } from "react";
import { ProfileHeader } from "@/components/profile/profile-header"
import { ProfileTabs } from "@/components/profile/profile-tabs"
import { createSupabaseServerClient } from "@/lib/supabase/client"
import { redirect } from "next/navigation"

export const metadata: Metadata = {
  title: "Your Profile | Introducing.day",
  description: "View and manage your Introducing.day profile",
}

export default async function ProfilePage() {
  const supabase = createSupabaseServerClient()

  // Get the current user session
  const { data: { session } } = await supabase.auth.getSession()

  // 移除重定向逻辑，允许未登录用户查看页面
  // if (!session) {
  //   redirect("/signin")
  // }

  // Get user data (可能为空)
  let user = null
  let upvotedProducts = []

  if (session) {
    // 用户已登录，获取用户数据
    const { data: userData } = await supabase.auth.getUser()
    user = userData.user

    // 获取用户点赞的产品
    const { data: userVotes, error: votesError } = await supabase
      .from("user_votes")
      .select("product_id, vote_type, created_at")
      .eq("user_id", user?.id)
      .eq("vote_type", "upvote")
      .order("created_at", { ascending: false })

    if (votesError) {
      console.error("Error fetching user votes:", votesError)
    }

    // 获取用户点赞的产品详情
    const upvotedProductIds = userVotes?.map(vote => vote.product_id) || []

    if (upvotedProductIds.length > 0) {
      const { data: products, error: productsError } = await supabase
        .from("products")
        .select("*")
        .in("id", upvotedProductIds)
        .order("created_at", { ascending: false })

      if (productsError) {
        console.error("Error fetching products:", productsError)
      } else {
        upvotedProducts = products || []
      }
    }
  }
  // 注意：移除了未登录用户的示例产品逻辑
  // 未登录用户将看到空状态，这更符合预期

  return (
    <div className="container py-8 max-w-5xl">
      <ProfileHeader user={user} />
      <Suspense fallback={<div className="text-center p-8">Loading tabs...</div>}>
        <ProfileTabs user={user} upvotedProducts={upvotedProducts} />
      </Suspense>
    </div>
  )
}
